/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.1+4189"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const TIMER2  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider5       = system.clockTree["PLL_CLK1_DIV"];
divider5.divideValue = 4;

const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const gate8  = system.clockTree["MFPCLKGATE"];
gate8.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

ADC121.$name               = "ADC12_0";
ADC121.sampClkDiv          = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.powerDownMode       = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.adcMem0_name        = "VOL";
ADC121.sampleTime0         = "1ms";
ADC121.enabledInterrupts   = ["DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED"];
ADC121.peripheral.$assign  = "ADC1";
ADC121.adcPin0Config.$name = "ti_driverlib_gpio_GPIOPinGeneric8";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                          = "OLED";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "SCL";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].assignedPin  = "28";
GPIO1.associatedPins[1].$name        = "SDA";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].assignedPin  = "31";
GPIO1.associatedPins[2].$name        = "RES";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "14";
GPIO1.associatedPins[3].$name        = "DC";
GPIO1.associatedPins[3].assignedPort = "PORTB";
GPIO1.associatedPins[3].assignedPin  = "15";

GPIO2.$name                          = "LED";
GPIO2.associatedPins[0].$name        = "UserLED";
GPIO2.associatedPins[0].assignedPort = "PORTB";
GPIO2.associatedPins[0].assignedPin  = "8";

GPIO3.$name                              = "KEY";
GPIO3.associatedPins[0].$name            = "UserKEY";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].assignedPort     = "PORTA";
GPIO3.associatedPins[0].assignedPin      = "18";
GPIO3.associatedPins[0].internalResistor = "PULL_DOWN";

GPIO4.$name                               = "Encoder1";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name             = "E1A";
GPIO4.associatedPins[0].direction         = "INPUT";
GPIO4.associatedPins[0].interruptEn       = true;
GPIO4.associatedPins[0].internalResistor  = "PULL_UP";
GPIO4.associatedPins[0].assignedPort      = "PORTB";
GPIO4.associatedPins[0].assignedPin       = "20";
GPIO4.associatedPins[0].polarity          = "RISE";
GPIO4.associatedPins[0].interruptPriority = "0";
GPIO4.associatedPins[1].$name             = "E1B";
GPIO4.associatedPins[1].direction         = "INPUT";
GPIO4.associatedPins[1].assignedPort      = "PORTB";
GPIO4.associatedPins[1].assignedPin       = "24";
GPIO4.associatedPins[1].internalResistor  = "PULL_UP";
GPIO4.associatedPins[1].interruptEn       = true;
GPIO4.associatedPins[1].polarity          = "RISE";
GPIO4.associatedPins[1].interruptPriority = "0";

GPIO5.$name                               = "Encoder2";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name             = "E2A";
GPIO5.associatedPins[0].direction         = "INPUT";
GPIO5.associatedPins[0].internalResistor  = "PULL_UP";
GPIO5.associatedPins[0].assignedPort      = "PORTA";
GPIO5.associatedPins[0].assignedPin       = "25";
GPIO5.associatedPins[0].interruptEn       = true;
GPIO5.associatedPins[0].polarity          = "RISE";
GPIO5.associatedPins[0].interruptPriority = "0";
GPIO5.associatedPins[1].$name             = "E2B";
GPIO5.associatedPins[1].direction         = "INPUT";
GPIO5.associatedPins[1].assignedPort      = "PORTA";
GPIO5.associatedPins[1].assignedPin       = "26";
GPIO5.associatedPins[1].interruptEn       = true;
GPIO5.associatedPins[1].internalResistor  = "PULL_UP";
GPIO5.associatedPins[1].polarity          = "RISE";
GPIO5.associatedPins[1].interruptPriority = "0";

GPIO6.$name                          = "MOTOR_DIR";
GPIO6.associatedPins.create(4);
GPIO6.associatedPins[0].$name        = "AIN1";
GPIO6.associatedPins[0].initialValue = "SET";
GPIO6.associatedPins[0].assignedPort = "PORTA";
GPIO6.associatedPins[0].assignedPin  = "16";
GPIO6.associatedPins[1].$name        = "AIN2";
GPIO6.associatedPins[1].initialValue = "SET";
GPIO6.associatedPins[1].assignedPort = "PORTA";
GPIO6.associatedPins[1].assignedPin  = "17";
GPIO6.associatedPins[2].$name        = "BIN1";
GPIO6.associatedPins[2].assignedPort = "PORTA";
GPIO6.associatedPins[2].assignedPin  = "14";
GPIO6.associatedPins[2].initialValue = "SET";
GPIO6.associatedPins[3].$name        = "BIN2";
GPIO6.associatedPins[3].initialValue = "SET";
GPIO6.associatedPins[3].assignedPort = "PORTA";
GPIO6.associatedPins[3].assignedPin  = "13";

GPIO7.$name                              = "MPU6050";
GPIO7.associatedPins[0].$name            = "INT_PIN";
GPIO7.associatedPins[0].direction        = "INPUT";
GPIO7.associatedPins[0].internalResistor = "PULL_UP";
GPIO7.associatedPins[0].assignedPort     = "PORTA";
GPIO7.associatedPins[0].assignedPin      = "7";
GPIO7.associatedPins[0].interruptEn      = true;
GPIO7.associatedPins[0].polarity         = "FALL";
GPIO7.associatedPins[0].pin.$assign      = "PA7";

GPIO8.$name                              = "GPIO_GRP_0";
GPIO8.associatedPins.create(8);
GPIO8.associatedPins[0].$name            = "KEY0";
GPIO8.associatedPins[0].direction        = "INPUT";
GPIO8.associatedPins[0].internalResistor = "PULL_UP";
GPIO8.associatedPins[0].pin.$assign      = "PA8";
GPIO8.associatedPins[1].$name            = "HDPIN_0";
GPIO8.associatedPins[1].direction        = "INPUT";
GPIO8.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[1].pin.$assign      = "PA9";
GPIO8.associatedPins[2].$name            = "HDPIN_1";
GPIO8.associatedPins[2].direction        = "INPUT";
GPIO8.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[2].pin.$assign      = "PA27";
GPIO8.associatedPins[3].$name            = "HDPIN_2";
GPIO8.associatedPins[3].direction        = "INPUT";
GPIO8.associatedPins[3].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[3].pin.$assign      = "PA24";
GPIO8.associatedPins[4].$name            = "HDPIN_3";
GPIO8.associatedPins[4].direction        = "INPUT";
GPIO8.associatedPins[4].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[4].pin.$assign      = "PB16";
GPIO8.associatedPins[5].$name            = "HDPIN_4";
GPIO8.associatedPins[5].direction        = "INPUT";
GPIO8.associatedPins[5].pin.$assign      = "PA12";
GPIO8.associatedPins[6].$name            = "HDPIN_5";
GPIO8.associatedPins[6].direction        = "INPUT";
GPIO8.associatedPins[6].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[6].pin.$assign      = "PB6";
GPIO8.associatedPins[7].$name            = "HDPIN_6";
GPIO8.associatedPins[7].direction        = "INPUT";
GPIO8.associatedPins[7].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[7].pin.$assign      = "PB7";

I2C1.$name                           = "I2C_0";
I2C1.basicEnableController           = true;
I2C1.basicControllerStandardBusSpeed = "Fast";
I2C1.peripheral.$assign              = "I2C0";
I2C1.sdaPinConfig.$name              = "ti_driverlib_gpio_GPIOPinGeneric6";
I2C1.sclPinConfig.$name              = "ti_driverlib_gpio_GPIOPinGeneric7";

PWM1.$name                      = "PWM_0";
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.timerCount                 = 8000;
PWM1.peripheral.$assign         = "TIMA1";
PWM1.peripheral.ccp0Pin.$assign = "PB2";
PWM1.peripheral.ccp1Pin.$assign = "PB3";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.systickEnable = true;
SYSTICK.periodEnable  = true;
SYSTICK.period        = 16777215;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkPrescale   = 80;
TIMER1.timerMode          = "PERIODIC";
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerPeriod        = "5ms";
TIMER1.peripheral.$assign = "TIMG0";

TIMER2.$name              = "DebugTimer";
TIMER2.timerMode          = "PERIODIC_UP";
TIMER2.timerPeriod        = "655.36ms";
TIMER2.timerClkDiv        = 8;
TIMER2.timerClkPrescale   = 100;
TIMER2.peripheral.$assign = "TIMG6";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                    = "UART_2";
UART2.enabledInterrupts        = ["RX"];
UART2.targetBaudRate           = 115200;
UART2.uartClkDiv               = "2";
UART2.interruptPriority        = "3";
UART2.peripheral.rxPin.$assign = "PA22";
UART2.peripheral.txPin.$assign = "PB17";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric9";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric10";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.adcPin0.$suggestSolution   = "PA15";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution = "PA28";
GPIO1.associatedPins[1].pin.$suggestSolution = "PA31";
GPIO1.associatedPins[2].pin.$suggestSolution = "PB14";
GPIO1.associatedPins[3].pin.$suggestSolution = "PB15";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB9";
GPIO3.associatedPins[0].pin.$suggestSolution = "PA18";
GPIO4.associatedPins[0].pin.$suggestSolution = "PB20";
GPIO4.associatedPins[1].pin.$suggestSolution = "PB24";
GPIO5.associatedPins[0].pin.$suggestSolution = "PA25";
GPIO5.associatedPins[1].pin.$suggestSolution = "PA26";
GPIO6.associatedPins[0].pin.$suggestSolution = "PA16";
GPIO6.associatedPins[1].pin.$suggestSolution = "PA17";
GPIO6.associatedPins[2].pin.$suggestSolution = "PA14";
GPIO6.associatedPins[3].pin.$suggestSolution = "PA13";
I2C1.peripheral.sdaPin.$suggestSolution      = "PA0";
I2C1.peripheral.sclPin.$suggestSolution      = "PA1";
UART1.peripheral.$suggestSolution            = "UART0";
UART2.peripheral.$suggestSolution            = "UART2";
