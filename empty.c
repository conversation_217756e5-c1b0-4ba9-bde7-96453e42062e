/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include <stdbool.h>    // 支持bool类型
#include <stdlib.h>     // 支持abs函数
#include <math.h>       // 支持数学函数
#include <stdio.h>      // 支持printf函数

#include "bsp_debugtimer.h"
#include "bsp_systick.h"
#include "bsp_oled.h"
#include "bsp_siic.h"
#include "balance.h"
#include "bsp_printf.h"

//#include "bluetooth.h"
#include "show.h"
#include "MPU6050.h"
//extern uint8_t Flag_Stop;
extern int Turn1;
extern int Velocity1;

int x=0,y=0,gy=0;



//按键控制模块 - 简单暴力版
static int circle_num = 0;             //圈数选择(0-4对应1-5圈)
static bool car_started = false;       //小车是否已启动
static bool btn_circle_pressed = false; //按键1按下状态
static bool btn_start_pressed = false;  //按键2按下状态

//全亮检测模块 - 灰度传感器全亮状态计数
static int target_bright_count = 0;     //目标全亮次数(4*圈数)
static int current_bright_count = 0;    //当前全亮计数
static bool last_all_bright = false;    //上次全亮状态
//用于检查 5ms 任务频率是否正确
pRtosDebugInterface_t mainTaskFreqCheck = &RTOSTaskDebug;
RtosDebugPrivateVar mainFreqPriv = { 0 };
uint8_t mainTaskFreq = 0;
float xunji(void);
bool check_all_sensors_bright(void);            //检测HD0-HD5是否全部亮起

//按键处理函数声明
void handle_buttons(void);                      //处理按键输入

int xuanti=1;
float m6050init=0;
//用于检查 5ms 任务总体运行的时间
RtosDebugPrivateVar mainUseTimePriv = { 0 };
float mainTaskUseTime = 0;
int i=0,KEY0=1,HD0=0,HD1=0,HD2=0,HD3=0,HD4=0,HD5=0,HD6=0,HD7=0,fx=0,state6050=0;
	int time=0;  
int main(void)
{
    // ===== 最小化初始化，避免干扰 =====
    // 只初始化最基本的系统时钟，不调用SYSCFG_DL_init()

    // 1. 手动使能GPIOB时钟
    GPIOB->GPRCM.RSTCTL = 0xB1000003;
    GPIOB->GPRCM.PWREN = 0x26000001;
    while ((GPIOB->GPRCM.STAT & 0x1) != 0x1);

    // 2. 关闭所有中断，避免干扰
    __disable_irq();

    // 3. 配置PB10为输出模式
    GPIOB->DOE31_0 |= (1 << 10);  // 使能PB10输出

    // 4. 强制输出低电平，让LED常亮
    GPIOB->DOUT31_0 &= ~(1 << 10);  // 输出0V，LED应该亮

    // 5. 死循环保持常亮状态
    while(1) {
        // 持续强制输出低电平，防止被其他代码干扰
        GPIOB->DOUT31_0 &= ~(1 << 10);
    }
    // ===== LED测试代码结束 =====

    // 跳过所有外设初始化，只做最基础的LED测试
    // 启动PWM定时器（用于电机测试）
    DL_TimerA_startCounter(PWM_0_INST);

    // 恢复到之前双电机都能转动的组合（虽然方向相反）
    // 左电机：AIN2=1, AIN1=0
    // 右电机：BIN1=1, BIN2=0

    // 前进测试：由于当前是后退，所以反向
    // 左电机：AIN1=1, AIN2=0（反向）
    // 右电机：BIN1=1, BIN2=0（反向）
    DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN | MOTOR_DIR_BIN1_PIN);
    DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN | MOTOR_DIR_BIN2_PIN);

    while (1) {
        // LED指示系统运行
        static int led_count = 0;
        led_count++;
        if(led_count > 100000) {
            DL_GPIO_togglePins(LED_PORT, LED_UserLED_PIN);
            led_count = 0;
        }

        // 处理按键输入
        handle_buttons();

        // 循迹控制 - 只有在小车启动后才执行
        static int control_count = 0;
        control_count++;
        if(car_started && control_count >= 10000) {  // 约10ms执行一次循迹
            // 全亮状态检测和计数
            bool current_all_bright = check_all_sensors_bright();
            if(current_all_bright && !last_all_bright) {
                current_bright_count++;
            }
            last_all_bright = current_all_bright;

            // 检查全亮计数，如果到达目标则停车
            if(current_bright_count >= target_bright_count) {
                // 停车：直接设置PWM为0
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C0_IDX);
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
                break;  // 彻底退出主循环，像之前一样
            }
            else {
                // 只有在未停车时才执行循迹
                // 读取灰度传感器并计算转向
                float turn_value = xunji();  // 调用循迹函数

                // 基础前进速度 - 固定速度
                int base_speed = 1800;  // 固定前进速度

                // 根据循迹结果调整左右电机速度
                int left_speed = base_speed - (int)(turn_value * 65);   // 转向系数65
                int right_speed = base_speed + (int)(turn_value * 65);

                // 限制PWM范围
                if(left_speed > 6000) left_speed = 6000;
                if(left_speed < 1000) left_speed = 1000;
                if(right_speed > 6000) right_speed = 6000;
                if(right_speed < 1000) right_speed = 1000;

                // 输出PWM到电机
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, left_speed, GPIO_PWM_0_C0_IDX);
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, right_speed, GPIO_PWM_0_C1_IDX);
            }

            control_count = 0;
        }
    }

    // 停车后状态指示 - 电机完全停止
    while(1) {
        // 确保电机完全停止
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C0_IDX);
        DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
        for(int i = 0; i < 1000000; i++);  // 简单延时
    }
}

//外部中断：编码器、dmp相关内容
void GROUP1_IRQHandler(void)//Group1的中断服务函数
{
	uint32_t portE2_intp = DL_GPIO_getEnabledInterruptStatus(Encoder2_PORT,MPU6050_INT_PIN_PIN);

	//MPU6050下降沿中断,频率可以通过DMP配置,默认200Hz
	if( (portE2_intp&MPU6050_INT_PIN_PIN)==MPU6050_INT_PIN_PIN )
	{
		mainTaskFreqCheck->TickStart(&mainUseTimePriv);//从此处开始计时
		Read_DMP();
		mpu6050.pitch = Roll;
		mpu6050.roll = Pitch;
		mpu6050.yaw = Yaw;
		mpu6050.gyro.x=gyro[0];
		mpu6050.gyro.y=gyro[1];
		mpu6050.gyro.z=gyro[2];
		mpu6050.accel.x=accel[0];
		mpu6050.accel.y=accel[0];
		mpu6050.accel.z=accel[0];

		//5ms执行1次控制
		BalanceControlTask();

		//清空标志位
		DL_GPIO_clearInterruptStatus(Encoder2_PORT,MPU6050_INT_PIN_PIN);

		//计算中断的频率,频率正确 mainTaskFreq 应该等于200
		mainTaskFreq = mainTaskFreqCheck->UpdateFreq(&mainFreqPriv);

		//计算运行到此处的耗时,单位ms
		mainTaskUseTime = mainTaskFreqCheck->UpdateUsedTime(&mainUseTimePriv);
	}
}

//串口0,用于调试
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST)) 
    {
        case DL_UART_IIDX_RX:
            DL_UART_transmitData(UART_0_INST,DL_UART_Main_receiveData(UART_0_INST));
            break;
        default:
            break;
    }
		
}
int ii=0;
int openmv[10];
int state = 0;
void K210_Receive_Data(int8_t data)//接收K210传过来的数据
{
	int j;

		
	if(state==0&&data==35)
	{
		state = 1;
		openmv[0] = data;
	}
	else if(state==1&&data==42)
	{
		state = 2;
		openmv[1] = data-48;
	}
	else if(state==2)
	{
		state = 3;
		openmv[2] = data-48;
	}
	else if(state==3)
	{
		state = 4;
		openmv[3] = data-48;
	}
	else if(state==4)
	{
		state = 5;
		openmv[4] = data-48;
	}
	else if(state==5)
	{
		state = 6;
		openmv[5] = data-48;
	}
	else if(state==6)
	{
		state = 7;
		openmv[6] = data-48;
	}
	else if(state==7)
	{
		state = 8;
		openmv[7] = data-48;
	}
	else if(state==8 && data==42)
	{
		state = 9;
		openmv[8] = data-48;
	}
	else if(state==9 )
	{
			if(data == 38)
			{
			state = 0;
			openmv[9] = data;
		
			x=openmv[2]*100+openmv[3]*10+openmv[4];
			y=openmv[5]*100+openmv[6]*10+openmv[7];
			}
			else
			{
				state = 0;
				for(j=0;j<10;j++)
				{
				openmv[j] = 0;
				}
			}
	}
	else
	{
		state = 0;
		for(j=0;j<10;j++)
		{
			openmv[j] = 0;
		}
	}
	if(data==38)
	{
		state=0;
		for(j=0;j<10;j++)
		{
			openmv[j] = 0;
		}
	}
}
	int16_t data1;
void UART_2_INST_IRQHandler(void)
{
	int data;
	switch (DL_UART_Main_getPendingInterrupt(UART_2_INST)) 
    {
		
        case DL_UART_IIDX_RX:
				    data=DL_UART_Main_receiveData(UART_2_INST);
						data1=data;
					  K210_Receive_Data(data);
//						DL_GPIO_togglePins(LED_PORT,LED_UserLED_PIN);
						
            break;
        default:
            break;
    }

}
	int gyst=0;
float xunji(void)
{
	float score=0;

	if(Flag_Stop == 0)
			{
				
				HD0=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_0_PORT, GPIO_GRP_0_HDPIN_0_PIN);
				HD1=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_1_PORT, GPIO_GRP_0_HDPIN_1_PIN);
				HD2=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_2_PORT, GPIO_GRP_0_HDPIN_2_PIN);
				HD3=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_3_PORT, GPIO_GRP_0_HDPIN_3_PIN);
				HD4=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_4_PORT, GPIO_GRP_0_HDPIN_4_PIN);
				HD5=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_5_PORT, GPIO_GRP_0_HDPIN_5_PIN);
			}
			if(HD5>0 && gyst==0)gyst=1;
			if(HD5==0 && gyst!=0)
			{
				
				gy+=1,gyst=0;
				while(1)
				{
						HD1=DL_GPIO_readPins(GPIO_GRP_0_HDPIN_1_PORT, GPIO_GRP_0_HDPIN_1_PIN);
					if(HD1>0)break;
					Turn1=-60;
				}
			}
	if (HD0> 0)//按键按下
		{
			score+=15;
		}
		
	if (HD1 > 0)//按键按下
		{
			score+=10;
		}
	if (HD2 > 0)//按键按下
		{
			score+=4;
		}
	if (HD3 > 0)//按键按下
		{
			score+=-4;
		}
	
	////////////////////////////////////////////////////////////////////	
		if (HD4 > 0)//按键按下
		{
			score+=-10;
		}
		
	if (HD5 > 0)//按键按下
		{
			score+=-15;
		}



	return score;
}

//全亮检测函数 - 检测HD0-HD5是否全部亮起
bool check_all_sensors_bright(void)
{
    // 检查每个传感器状态
    bool all_bright = (HD0 > 0 && HD1 > 0 && HD2 > 0 && HD3 > 0 && HD4 > 0 && HD5 > 0);

    // 如果检测到全亮，闪烁LED作为指示
    // 注意：您的LED连接方式需要低电平点亮
    if(all_bright) {
        DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);  // 点亮LED（低电平）
    } else {
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);    // 熄灭LED（高电平）
    }

    return all_bright;
}

//按键处理函数
void handle_buttons(void)
{
    // 读取按键状态 (按下为低电平，因为使用内置上拉)
    bool btn_circle_current = (DL_GPIO_readPins(BTN_CIRCLE_PORT, BTN_CIRCLE_PIN) == 0);
    bool btn_start_current = (DL_GPIO_readPins(BTN_START_PORT, BTN_START_PIN) == 0);

    // 按键1：圈数选择 (检测按下边沿)
    if(btn_circle_current && !btn_circle_pressed) {
        circle_num = (circle_num + 1) % 5;  // 循环0-4对应1-5圈
        if(circle_num == 0) circle_num = 1;  // 确保至少是1圈
        target_bright_count = circle_num * 4;  // 设置目标全亮次数

        // 用LED闪烁次数指示当前圈数
        for(int i = 0; i <= circle_num; i++) {
            DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);
            for(int j = 0; j < 200000; j++);  // 延时
            DL_GPIO_clearPins(LED_PORT, LED_UserLED_PIN);
            for(int j = 0; j < 200000; j++);  // 延时
        }
    }
    btn_circle_pressed = btn_circle_current;

    // 按键2：启动小车 (检测按下边沿) - 一次性启动
    if(btn_start_current && !btn_start_pressed && !car_started) {
        car_started = true;               // 启动小车
        // 重置全亮计数状态
        current_bright_count = 0;
        last_all_bright = false;
    }
    btn_start_pressed = btn_start_current;
}

